from autogen_agentchat.agents import AssistantAgent, UserProxyAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from groq import Groq
import os
from dotenv import load_dotenv

load_dotenv()

GROQ_MODEL_INFO = {
    "family": "llama3",
    "context_length": 8192,
    "vision": False,
    "function_calling": <PERSON>alse,
    "tool_choice": False,
    "parallel_tool_calls": False,
    "json_mode": True,
    "json_output": True,
    "structured_output": True,
}

class GroqChatCompletionClient(OpenAIChatCompletionClient):
    def __init__(self, api_key: str, model: str = "whisper-large-v3"):
        super().__init__(
            api_key=api_key,
            base_url="https://api.groq.com/openai/v1",
            model=model,
            model_info=GROQ_MODEL_INFO
        )

# Initialize Groq client
groq_client = GroqChatCompletionClient(
    api_key=os.getenv("GROQ_API_KEY")
)



assistant = AssistantAgent( name="assistant", 
                           model_client=groq_client
                           )

result = await assistant.run( task="What is the capital City of Kenya")

print(result)

result.messages[-1].content

assistant_2 = AssistantAgent( name="assistant", 
                           model_client=groq_client,
                           description="Give output in JSON format.",
                           system_message="You are a helpful assistant that answers questions about history"
                           )

result_2 = await assistant_2.run( task="What is the capital City of Kenya")

print(result)

result_2.messages[1].content

from io import BytesIO

import requests
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import Image as AGImage
from PIL import Image

pil_image = Image.open(BytesIO(requests.get("https://picsum.photos/300/200").content))
img = AGImage(pil_image)
multi_modal_message = MultiModalMessage(content=["Can you describe the content of this image?", img], source="User")
img


multi_modal_message = MultiModalMessage(content=["Can you describe the content of this image?", img], 
                                        source="User")
result_3 = await assistant_2.run( task=multi_modal_message)

result_3.messages[-1].content

import os
import torch
import requests
from io import BytesIO
from PIL import Image

from transformers import AutoProcessor, LlavaForConditionalGeneration

from autogen import AssistantAgent
from autogen_agentchat.messages import MultiModalMessage
from autogen_core import Image as AGImage


# ✅ Step 1: Define LLaVA Agent
class LlavaImageAssistant(AssistantAgent):
    def __init__(self, name="assistant_2", model_id="llava-hf/llava-1.6-34b-hf"):
        super().__init__(name=name)
        self.processor = AutoProcessor.from_pretrained(model_id)
        self.model = LlavaForConditionalGeneration.from_pretrained(
            model_id,
            torch_dtype=torch.float16,
            device_map="auto"
        )

    async def run(self, task):
        if isinstance(task, MultiModalMessage):
            # Extract PIL Image and prompt
            prompt = task.content[0]
            ag_image = task.content[1]
            pil_image = ag_image.data

            # Process with LLaVA
            inputs = self.processor(prompt, images=pil_image, return_tensors="pt").to("cuda")
            generate_ids = self.model.generate(**inputs, max_new_tokens=100)
            response = self.processor.batch_decode(generate_ids, skip_special_tokens=True)[0]

            # Respond with content
            return MultiModalMessage(content=response, source=self.name)
        else:
            return MultiModalMessage(content="Unsupported task type.", source=self.name)

# ✅ Step 2: Initialize the Assistant
assistant_2 = LlavaImageAssistant()

# ✅ Step 3: Create a MultiModal Task
img_url = "https://picsum.photos/300/200"
pil_image = Image.open(BytesIO(requests.get(img_url).content))
img = AGImage(pil_image)

multi_modal_message = MultiModalMessage(
    content=["Can you describe the content of this image?", img],
    source="User"
)

# ✅ Step 4: Run the Agent (async)
import asyncio

async def main():
    result_3 = await assistant_2.run(task=multi_modal_message)
    print("🔍 Response:", result_3.content)

asyncio.run(main())
